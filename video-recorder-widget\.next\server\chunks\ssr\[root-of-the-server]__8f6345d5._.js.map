{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/video_app/video-recorder-widget/src/components/VideoRecorder.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Play, Square, RotateCcw, Upload, Camera, Loader2 } from 'lucide-react';\n\ninterface VideoRecorderProps {\n  onVideoUploaded?: (videoUrl: string) => void;\n  contactId?: string;\n  maxDuration?: number; // in seconds\n  className?: string;\n}\n\ntype RecordingState = 'idle' | 'recording' | 'stopped' | 'uploading';\n\nexport default function VideoRecorder({ \n  onVideoUploaded, \n  contactId, \n  maxDuration = 300, // 5 minutes default\n  className = '' \n}: VideoRecorderProps) {\n  const [recordingState, setRecordingState] = useState<RecordingState>('idle');\n  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [error, setError] = useState<string | null>(null);\n  const [uploadProgress, setUploadProgress] = useState(0);\n\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const streamRef = useRef<MediaStream | null>(null);\n  const chunksRef = useRef<Blob[]>([]);\n  const timerRef = useRef<NodeJS.Timeout | null>(null);\n\n  const startRecording = useCallback(async () => {\n    try {\n      setError(null);\n      \n      // Request camera and microphone access\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: { ideal: 1280 },\n          height: { ideal: 720 },\n          facingMode: 'user'\n        },\n        audio: true\n      });\n\n      streamRef.current = stream;\n      \n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n\n      // Create MediaRecorder\n      const mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'video/webm;codecs=vp9,opus'\n      });\n\n      mediaRecorderRef.current = mediaRecorder;\n      chunksRef.current = [];\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunksRef.current.push(event.data);\n        }\n      };\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunksRef.current, { type: 'video/webm' });\n        setRecordedBlob(blob);\n        setRecordingState('stopped');\n        \n        // Create preview URL\n        if (videoRef.current) {\n          videoRef.current.srcObject = null;\n          videoRef.current.src = URL.createObjectURL(blob);\n        }\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setRecordingState('recording');\n      setRecordingTime(0);\n\n      // Start timer\n      timerRef.current = setInterval(() => {\n        setRecordingTime(prev => {\n          const newTime = prev + 1;\n          if (newTime >= maxDuration) {\n            stopRecording();\n          }\n          return newTime;\n        });\n      }, 1000);\n\n    } catch (err) {\n      console.error('Error starting recording:', err);\n      setError('Failed to access camera/microphone. Please check permissions.');\n    }\n  }, [maxDuration]);\n\n  const stopRecording = useCallback(() => {\n    if (mediaRecorderRef.current && recordingState === 'recording') {\n      mediaRecorderRef.current.stop();\n    }\n\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop());\n      streamRef.current = null;\n    }\n\n    if (timerRef.current) {\n      clearInterval(timerRef.current);\n      timerRef.current = null;\n    }\n  }, [recordingState]);\n\n  const resetRecording = useCallback(() => {\n    setRecordedBlob(null);\n    setRecordingState('idle');\n    setRecordingTime(0);\n    setError(null);\n    setUploadProgress(0);\n\n    if (videoRef.current) {\n      videoRef.current.src = '';\n      videoRef.current.srcObject = null;\n    }\n  }, []);\n\n  const uploadVideo = useCallback(async () => {\n    if (!recordedBlob) return;\n\n    try {\n      setRecordingState('uploading');\n      setUploadProgress(0);\n\n      // Create FormData for upload\n      const formData = new FormData();\n      formData.append('video', recordedBlob, `recording-${Date.now()}.webm`);\n      if (contactId) {\n        formData.append('contactId', contactId);\n      }\n\n      // Upload to our API endpoint\n      const response = await fetch('/api/upload-video', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error('Upload failed');\n      }\n\n      const result = await response.json();\n      \n      if (result.videoUrl) {\n        onVideoUploaded?.(result.videoUrl);\n        setUploadProgress(100);\n        // Reset after successful upload\n        setTimeout(resetRecording, 2000);\n      }\n\n    } catch (err) {\n      console.error('Upload error:', err);\n      setError('Failed to upload video. Please try again.');\n      setRecordingState('stopped');\n    }\n  }, [recordedBlob, contactId, onVideoUploaded, resetRecording]);\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>\n      <div className=\"space-y-4\">\n        {/* Video Preview */}\n        <div className=\"relative bg-gray-900 rounded-lg overflow-hidden aspect-video\">\n          <video\n            ref={videoRef}\n            autoPlay\n            muted={recordingState === 'recording'}\n            controls={recordingState === 'stopped'}\n            className=\"w-full h-full object-cover\"\n          />\n          \n          {recordingState === 'idle' && (\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <Camera className=\"w-16 h-16 text-gray-400\" />\n            </div>\n          )}\n\n          {recordingState === 'recording' && (\n            <div className=\"absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\" />\n              REC {formatTime(recordingTime)}\n            </div>\n          )}\n        </div>\n\n        {/* Controls */}\n        <div className=\"flex justify-center gap-3\">\n          {recordingState === 'idle' && (\n            <button\n              onClick={startRecording}\n              className=\"bg-red-500 hover:bg-red-600 text-white p-3 rounded-full transition-colors\"\n              title=\"Start Recording\"\n            >\n              <Play className=\"w-6 h-6\" />\n            </button>\n          )}\n\n          {recordingState === 'recording' && (\n            <button\n              onClick={stopRecording}\n              className=\"bg-gray-800 hover:bg-gray-900 text-white p-3 rounded-full transition-colors\"\n              title=\"Stop Recording\"\n            >\n              <Square className=\"w-6 h-6\" />\n            </button>\n          )}\n\n          {recordingState === 'stopped' && (\n            <>\n              <button\n                onClick={resetRecording}\n                className=\"bg-gray-500 hover:bg-gray-600 text-white p-3 rounded-full transition-colors\"\n                title=\"Record Again\"\n              >\n                <RotateCcw className=\"w-6 h-6\" />\n              </button>\n              <button\n                onClick={uploadVideo}\n                className=\"bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full transition-colors\"\n                title=\"Upload Video\"\n              >\n                <Upload className=\"w-6 h-6\" />\n              </button>\n            </>\n          )}\n\n          {recordingState === 'uploading' && (\n            <div className=\"bg-blue-500 text-white p-3 rounded-full\">\n              <Loader2 className=\"w-6 h-6 animate-spin\" />\n            </div>\n          )}\n        </div>\n\n        {/* Upload Progress */}\n        {recordingState === 'uploading' && (\n          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n            <div \n              className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n              style={{ width: `${uploadProgress}%` }}\n            />\n          </div>\n        )}\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm\">\n            {error}\n          </div>\n        )}\n\n        {/* Recording Info */}\n        <div className=\"text-center text-sm text-gray-500\">\n          {recordingState === 'idle' && 'Click the record button to start'}\n          {recordingState === 'recording' && `Recording... (${formatTime(maxDuration - recordingTime)} remaining)`}\n          {recordingState === 'stopped' && 'Review your recording and upload when ready'}\n          {recordingState === 'uploading' && 'Uploading your video...'}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAce,SAAS,cAAc,EACpC,eAAe,EACf,SAAS,EACT,cAAc,GAAG,EACjB,YAAY,EAAE,EACK;IACnB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAC7C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU,EAAE;IACnC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAE/C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI;YACF,SAAS;YAET,uCAAuC;YACvC,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBACL,OAAO;wBAAE,OAAO;oBAAK;oBACrB,QAAQ;wBAAE,OAAO;oBAAI;oBACrB,YAAY;gBACd;gBACA,OAAO;YACT;YAEA,UAAU,OAAO,GAAG;YAEpB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;YAC/B;YAEA,uBAAuB;YACvB,MAAM,gBAAgB,IAAI,cAAc,QAAQ;gBAC9C,UAAU;YACZ;YAEA,iBAAiB,OAAO,GAAG;YAC3B,UAAU,OAAO,GAAG,EAAE;YAEtB,cAAc,eAAe,GAAG,CAAC;gBAC/B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;oBACvB,UAAU,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI;gBACnC;YACF;YAEA,cAAc,MAAM,GAAG;gBACrB,MAAM,OAAO,IAAI,KAAK,UAAU,OAAO,EAAE;oBAAE,MAAM;gBAAa;gBAC9D,gBAAgB;gBAChB,kBAAkB;gBAElB,qBAAqB;gBACrB,IAAI,SAAS,OAAO,EAAE;oBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;oBAC7B,SAAS,OAAO,CAAC,GAAG,GAAG,IAAI,eAAe,CAAC;gBAC7C;YACF;YAEA,kBAAkB;YAClB,cAAc,KAAK,CAAC,OAAO,4BAA4B;YACvD,kBAAkB;YAClB,iBAAiB;YAEjB,cAAc;YACd,SAAS,OAAO,GAAG,YAAY;gBAC7B,iBAAiB,CAAA;oBACf,MAAM,UAAU,OAAO;oBACvB,IAAI,WAAW,aAAa;wBAC1B;oBACF;oBACA,OAAO;gBACT;YACF,GAAG;QAEL,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,iBAAiB,OAAO,IAAI,mBAAmB,aAAa;YAC9D,iBAAiB,OAAO,CAAC,IAAI;QAC/B;QAEA,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YACzD,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,SAAS,OAAO,EAAE;YACpB,cAAc,SAAS,OAAO;YAC9B,SAAS,OAAO,GAAG;QACrB;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,SAAS;QACT,kBAAkB;QAElB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,GAAG,GAAG;YACvB,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,kBAAkB;YAClB,kBAAkB;YAElB,6BAA6B;YAC7B,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,cAAc,CAAC,UAAU,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;YACrE,IAAI,WAAW;gBACb,SAAS,MAAM,CAAC,aAAa;YAC/B;YAEA,6BAA6B;YAC7B,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,QAAQ,EAAE;gBACnB,kBAAkB,OAAO,QAAQ;gBACjC,kBAAkB;gBAClB,gCAAgC;gBAChC,WAAW,gBAAgB;YAC7B;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,SAAS;YACT,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAc;QAAW;QAAiB;KAAe;IAE7D,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAK;4BACL,QAAQ;4BACR,OAAO,mBAAmB;4BAC1B,UAAU,mBAAmB;4BAC7B,WAAU;;;;;;wBAGX,mBAAmB,wBAClB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;wBAIrB,mBAAmB,6BAClB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;gCAAgD;gCAC1D,WAAW;;;;;;;;;;;;;8BAMtB,8OAAC;oBAAI,WAAU;;wBACZ,mBAAmB,wBAClB,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;wBAInB,mBAAmB,6BAClB,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;wBAIrB,mBAAmB,2BAClB;;8CACE,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;wBAKvB,mBAAmB,6BAClB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMxB,mBAAmB,6BAClB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,GAAG,eAAe,CAAC,CAAC;wBAAC;;;;;;;;;;;gBAM1C,uBACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAKL,8OAAC;oBAAI,WAAU;;wBACZ,mBAAmB,UAAU;wBAC7B,mBAAmB,eAAe,CAAC,cAAc,EAAE,WAAW,cAAc,eAAe,WAAW,CAAC;wBACvG,mBAAmB,aAAa;wBAChC,mBAAmB,eAAe;;;;;;;;;;;;;;;;;;AAK7C", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/video_app/video-recorder-widget/src/components/EmbedCodeGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Copy, Check, ExternalLink } from 'lucide-react';\n\ninterface EmbedCodes {\n  iframe: string;\n  javascript: string;\n  gohighlevel: string;\n  responsive: string;\n}\n\nexport default function EmbedCodeGenerator() {\n  const [embedCodes, setEmbedCodes] = useState<EmbedCodes | null>(null);\n  const [previewUrl, setPreviewUrl] = useState<string>('');\n  const [loading, setLoading] = useState(false);\n  const [copiedCode, setCopiedCode] = useState<string | null>(null);\n  \n  // Form state\n  const [contactId, setContactId] = useState('{{contact.id}}');\n  const [firstName, setFirstName] = useState('{{contact.first_name}}');\n  const [lastName, setLastName] = useState('{{contact.last_name}}');\n  const [width, setWidth] = useState('100%');\n  const [height, setHeight] = useState('600');\n  const [customDomain, setCustomDomain] = useState('');\n\n  const generateEmbedCode = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/embed-code', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contactId,\n          firstName,\n          lastName,\n          width,\n          height,\n          customDomain,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to generate embed code');\n      }\n\n      const data = await response.json();\n      setEmbedCodes(data.embedCodes);\n      setPreviewUrl(data.previewUrl);\n    } catch (error) {\n      console.error('Error generating embed code:', error);\n      alert('Failed to generate embed code. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const copyToClipboard = async (code: string, type: string) => {\n    try {\n      await navigator.clipboard.writeText(code);\n      setCopiedCode(type);\n      setTimeout(() => setCopiedCode(null), 2000);\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error);\n      alert('Failed to copy to clipboard');\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Configuration Form */}\n      <div className=\"grid md:grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Contact ID\n          </label>\n          <input\n            type=\"text\"\n            value={contactId}\n            onChange={(e) => setContactId(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n            placeholder=\"{{contact.id}}\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            First Name\n          </label>\n          <input\n            type=\"text\"\n            value={firstName}\n            onChange={(e) => setFirstName(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n            placeholder=\"{{contact.first_name}}\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Last Name\n          </label>\n          <input\n            type=\"text\"\n            value={lastName}\n            onChange={(e) => setLastName(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n            placeholder=\"{{contact.last_name}}\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Custom Domain (Optional)\n          </label>\n          <input\n            type=\"text\"\n            value={customDomain}\n            onChange={(e) => setCustomDomain(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n            placeholder=\"https://your-domain.com\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Width\n          </label>\n          <input\n            type=\"text\"\n            value={width}\n            onChange={(e) => setWidth(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n            placeholder=\"100%\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Height\n          </label>\n          <input\n            type=\"text\"\n            value={height}\n            onChange={(e) => setHeight(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n            placeholder=\"600\"\n          />\n        </div>\n      </div>\n\n      {/* Generate Button */}\n      <div className=\"flex gap-3\">\n        <button\n          onClick={generateEmbedCode}\n          disabled={loading}\n          className=\"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-md transition-colors\"\n        >\n          {loading ? 'Generating...' : 'Generate Embed Code'}\n        </button>\n        \n        {previewUrl && (\n          <a\n            href={previewUrl}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors flex items-center gap-2\"\n          >\n            <ExternalLink className=\"w-4 h-4\" />\n            Preview\n          </a>\n        )}\n      </div>\n\n      {/* Generated Embed Codes */}\n      {embedCodes && (\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-medium text-gray-800\">Generated Embed Codes</h3>\n          \n          {/* GoHighLevel Embed */}\n          <div className=\"border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <h4 className=\"font-medium text-gray-700\">GoHighLevel Funnel (Recommended)</h4>\n              <button\n                onClick={() => copyToClipboard(embedCodes.gohighlevel, 'gohighlevel')}\n                className=\"flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800\"\n              >\n                {copiedCode === 'gohighlevel' ? (\n                  <>\n                    <Check className=\"w-4 h-4\" />\n                    Copied!\n                  </>\n                ) : (\n                  <>\n                    <Copy className=\"w-4 h-4\" />\n                    Copy\n                  </>\n                )}\n              </button>\n            </div>\n            <pre className=\"bg-gray-50 p-3 rounded text-xs overflow-x-auto\">\n              <code>{embedCodes.gohighlevel}</code>\n            </pre>\n          </div>\n\n          {/* Basic iframe */}\n          <div className=\"border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <h4 className=\"font-medium text-gray-700\">Basic iframe</h4>\n              <button\n                onClick={() => copyToClipboard(embedCodes.iframe, 'iframe')}\n                className=\"flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800\"\n              >\n                {copiedCode === 'iframe' ? (\n                  <>\n                    <Check className=\"w-4 h-4\" />\n                    Copied!\n                  </>\n                ) : (\n                  <>\n                    <Copy className=\"w-4 h-4\" />\n                    Copy\n                  </>\n                )}\n              </button>\n            </div>\n            <pre className=\"bg-gray-50 p-3 rounded text-xs overflow-x-auto\">\n              <code>{embedCodes.iframe}</code>\n            </pre>\n          </div>\n\n          {/* Responsive Embed */}\n          <div className=\"border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <h4 className=\"font-medium text-gray-700\">Responsive Embed</h4>\n              <button\n                onClick={() => copyToClipboard(embedCodes.responsive, 'responsive')}\n                className=\"flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800\"\n              >\n                {copiedCode === 'responsive' ? (\n                  <>\n                    <Check className=\"w-4 h-4\" />\n                    Copied!\n                  </>\n                ) : (\n                  <>\n                    <Copy className=\"w-4 h-4\" />\n                    Copy\n                  </>\n                )}\n              </button>\n            </div>\n            <pre className=\"bg-gray-50 p-3 rounded text-xs overflow-x-auto\">\n              <code>{embedCodes.responsive}</code>\n            </pre>\n          </div>\n\n          {/* JavaScript Embed */}\n          <div className=\"border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <h4 className=\"font-medium text-gray-700\">JavaScript Embed</h4>\n              <button\n                onClick={() => copyToClipboard(embedCodes.javascript, 'javascript')}\n                className=\"flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800\"\n              >\n                {copiedCode === 'javascript' ? (\n                  <>\n                    <Check className=\"w-4 h-4\" />\n                    Copied!\n                  </>\n                ) : (\n                  <>\n                    <Copy className=\"w-4 h-4\" />\n                    Copy\n                  </>\n                )}\n              </button>\n            </div>\n            <pre className=\"bg-gray-50 p-3 rounded text-xs overflow-x-auto\">\n              <code>{embedCodes.javascript}</code>\n            </pre>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAYe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,aAAa;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB;QACxB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA;oBACA;oBACA;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,cAAc,KAAK,UAAU;YAC7B,cAAc,KAAK,UAAU;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,cAAc;YACd,WAAW,IAAM,cAAc,OAAO;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAGhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAGhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAGhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAGhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAGhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,UAAU,kBAAkB;;;;;;oBAG9B,4BACC,8OAAC;wBACC,MAAM;wBACN,QAAO;wBACP,KAAI;wBACJ,WAAU;;0CAEV,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;YAOzC,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAGlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCACC,SAAS,IAAM,gBAAgB,WAAW,WAAW,EAAE;wCACvD,WAAU;kDAET,eAAe,8BACd;;8DACE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;yEAI/B;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;0CAMpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAM,WAAW,WAAW;;;;;;;;;;;;;;;;;kCAKjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCACC,SAAS,IAAM,gBAAgB,WAAW,MAAM,EAAE;wCAClD,WAAU;kDAET,eAAe,yBACd;;8DACE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;yEAI/B;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;0CAMpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAM,WAAW,MAAM;;;;;;;;;;;;;;;;;kCAK5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCACC,SAAS,IAAM,gBAAgB,WAAW,UAAU,EAAE;wCACtD,WAAU;kDAET,eAAe,6BACd;;8DACE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;yEAI/B;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;0CAMpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAM,WAAW,UAAU;;;;;;;;;;;;;;;;;kCAKhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCACC,SAAS,IAAM,gBAAgB,WAAW,UAAU,EAAE;wCACtD,WAAU;kDAET,eAAe,6BACd;;8DACE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;yEAI/B;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;0CAMpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAM,WAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C", "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/video_app/video-recorder-widget/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport VideoRecorder from '@/components/VideoRecorder';\nimport EmbedCodeGenerator from '@/components/EmbedCodeGenerator';\n\nexport default function Home() {\n  const [uploadedVideoUrl, setUploadedVideoUrl] = useState<string | null>(null);\n  const [contactId, setContactId] = useState('');\n\n  const handleVideoUploaded = (videoUrl: string) => {\n    setUploadedVideoUrl(videoUrl);\n    console.log('Video uploaded:', videoUrl);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            GoHighLevel Video Recorder\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto mb-6\">\n            Record videos directly in your funnels and automatically save them to your GoHighLevel contacts\n          </p>\n          <div className=\"flex justify-center gap-4\">\n            <a\n              href=\"/test\"\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md transition-colors\"\n            >\n              🧪 Test Page\n            </a>\n            <a\n              href=\"/embed\"\n              className=\"bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-md transition-colors\"\n            >\n              📱 Embed Preview\n            </a>\n          </div>\n        </div>\n\n        {/* Configuration Panel */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n          <h2 className=\"text-2xl font-semibold text-gray-800 mb-4\">Configuration</h2>\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            <div>\n              <label htmlFor=\"contactId\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Contact ID (Optional)\n              </label>\n              <input\n                type=\"text\"\n                id=\"contactId\"\n                value={contactId}\n                onChange={(e) => setContactId(e.target.value)}\n                placeholder=\"Enter GoHighLevel Contact ID\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                If provided, the video URL will be saved to this contact's custom field\n              </p>\n            </div>\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">Features</h3>\n              <ul className=\"text-sm text-gray-600 space-y-1\">\n                <li>• Record up to 5 minutes of video</li>\n                <li>• Automatic upload to Cloudflare R2</li>\n                <li>• GoHighLevel contact integration</li>\n                <li>• Embeddable in any funnel</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Video Recorder */}\n        <div className=\"mb-8\">\n          <VideoRecorder\n            onVideoUploaded={handleVideoUploaded}\n            contactId={contactId || undefined}\n            maxDuration={300}\n            className=\"mx-auto\"\n          />\n        </div>\n\n        {/* Upload Result */}\n        {uploadedVideoUrl && (\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-6\">\n            <h3 className=\"text-lg font-semibold text-green-800 mb-2\">\n              ✅ Video Uploaded Successfully!\n            </h3>\n            <p className=\"text-green-700 mb-3\">\n              Your video has been uploaded and is ready to use.\n            </p>\n            <div className=\"bg-white rounded border p-3\">\n              <p className=\"text-sm text-gray-600 mb-1\">Video URL:</p>\n              <code className=\"text-sm bg-gray-100 px-2 py-1 rounded break-all\">\n                {uploadedVideoUrl}\n              </code>\n            </div>\n            {contactId && (\n              <p className=\"text-sm text-green-600 mt-2\">\n                📝 Contact {contactId} has been updated with the video URL\n              </p>\n            )}\n          </div>\n        )}\n\n        {/* Embed Code Generator */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mt-8\">\n          <h2 className=\"text-2xl font-semibold text-gray-800 mb-4\">Embed Code Generator</h2>\n          <EmbedCodeGenerator />\n        </div>\n\n        {/* Integration Instructions */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mt-8\">\n          <h2 className=\"text-2xl font-semibold text-gray-800 mb-4\">Integration Guide</h2>\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-700 mb-3\">Embed in GoHighLevel</h3>\n              <ol className=\"text-sm text-gray-600 space-y-2\">\n                <li>1. Use the embed code generator above</li>\n                <li>2. Add a Custom HTML element to your funnel</li>\n                <li>3. Paste the generated embed code</li>\n                <li>4. Test with a sample contact</li>\n              </ol>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-700 mb-3\">Setup Requirements</h3>\n              <ul className=\"text-sm text-gray-600 space-y-2\">\n                <li>• Cloudflare R2 bucket configured</li>\n                <li>• Supabase project with edge function</li>\n                <li>• GoHighLevel API access</li>\n                <li>• Custom field for video URLs</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB;QACpB,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAA+C;;;;;;sDAGpF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5C,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAI5C,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;wBACZ,iBAAiB;wBACjB,WAAW,aAAa;wBACxB,aAAa;wBACb,WAAU;;;;;;;;;;;gBAKb,kCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;sCAGnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;wBAGJ,2BACC,8OAAC;4BAAE,WAAU;;gCAA8B;gCAC7B;gCAAU;;;;;;;;;;;;;8BAO9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,8OAAC,wIAAA,CAAA,UAAkB;;;;;;;;;;;8BAIrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}]}