'use client';

import { useState } from 'react';
import VideoRecorder from '@/components/VideoRecorder';

export default function TestPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isTestingAPI, setIsTestingAPI] = useState(false);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testAPIEndpoints = async () => {
    setIsTestingAPI(true);
    addTestResult('Starting API tests...');

    try {
      // Test presigned URL generation
      addTestResult('Testing presigned URL generation...');
      const presignedResponse = await fetch('/api/presigned-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ contactId: 'test-contact' })
      });

      if (presignedResponse.ok) {
        const presignedData = await presignedResponse.json();
        addTestResult('✅ Presigned URL generation successful');
        addTestResult(`Generated URL: ${presignedData.publicUrl}`);
      } else {
        addTestResult('❌ Presigned URL generation failed');
      }

      // Test embed code generation
      addTestResult('Testing embed code generation...');
      const embedResponse = await fetch('/api/embed-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contactId: 'test-contact',
          firstName: 'John',
          lastName: 'Doe'
        })
      });

      if (embedResponse.ok) {
        const embedData = await embedResponse.json();
        addTestResult('✅ Embed code generation successful');
        addTestResult(`Preview URL: ${embedData.previewUrl}`);
      } else {
        addTestResult('❌ Embed code generation failed');
      }

    } catch (error) {
      addTestResult(`❌ API test error: ${error}`);
    } finally {
      setIsTestingAPI(false);
      addTestResult('API tests completed');
    }
  };

  const handleVideoUploaded = (videoUrl: string) => {
    addTestResult(`✅ Video uploaded successfully: ${videoUrl}`);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Video Recorder Test Page
          </h1>
          <p className="text-gray-600">
            Test the video recording functionality and API endpoints
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Video Recorder Test */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Video Recorder Component
            </h2>
            <VideoRecorder
              onVideoUploaded={handleVideoUploaded}
              contactId="test-contact-123"
              maxDuration={60} // 1 minute for testing
            />
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-800 mb-2">Test Instructions:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>1. Click record to start recording</li>
                <li>2. Record a short test video (max 1 minute)</li>
                <li>3. Stop recording and review</li>
                <li>4. Click upload to test the upload process</li>
                <li>5. Check the test results panel for upload status</li>
              </ul>
            </div>
          </div>

          {/* Test Results Panel */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">
                Test Results
              </h2>
              <div className="space-x-2">
                <button
                  onClick={testAPIEndpoints}
                  disabled={isTestingAPI}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded text-sm"
                >
                  {isTestingAPI ? 'Testing...' : 'Test APIs'}
                </button>
                <button
                  onClick={clearResults}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm"
                >
                  Clear
                </button>
              </div>
            </div>

            <div className="bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
              {testResults.length === 0 ? (
                <div className="text-gray-500">
                  No test results yet. Start by testing the APIs or recording a video.
                </div>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Environment Check */}
        <div className="bg-white rounded-lg shadow-lg p-6 mt-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Environment Configuration Check
          </h2>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Required Environment Variables</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>NEXT_PUBLIC_R2_BUCKET_NAME:</span>
                  <span className={process.env.NEXT_PUBLIC_R2_BUCKET_NAME ? 'text-green-600' : 'text-red-600'}>
                    {process.env.NEXT_PUBLIC_R2_BUCKET_NAME ? '✅ Set' : '❌ Missing'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>NEXT_PUBLIC_R2_ENDPOINT:</span>
                  <span className={process.env.NEXT_PUBLIC_R2_ENDPOINT ? 'text-green-600' : 'text-red-600'}>
                    {process.env.NEXT_PUBLIC_R2_ENDPOINT ? '✅ Set' : '❌ Missing'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>NEXT_PUBLIC_SUPABASE_URL:</span>
                  <span className={process.env.NEXT_PUBLIC_SUPABASE_URL ? 'text-green-600' : 'text-red-600'}>
                    {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>NEXT_PUBLIC_SUPABASE_ANON_KEY:</span>
                  <span className={process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'text-green-600' : 'text-red-600'}>
                    {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}
                  </span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Browser Capabilities</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>MediaRecorder API:</span>
                  <span className={typeof window !== 'undefined' && 'MediaRecorder' in window ? 'text-green-600' : 'text-red-600'}>
                    {typeof window !== 'undefined' && 'MediaRecorder' in window ? '✅ Supported' : '❌ Not Supported'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>getUserMedia API:</span>
                  <span className={typeof window !== 'undefined' && navigator.mediaDevices?.getUserMedia ? 'text-green-600' : 'text-red-600'}>
                    {typeof window !== 'undefined' && navigator.mediaDevices?.getUserMedia ? '✅ Supported' : '❌ Not Supported'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>HTTPS:</span>
                  <span className={typeof window !== 'undefined' && window.location.protocol === 'https:' ? 'text-green-600' : 'text-yellow-600'}>
                    {typeof window !== 'undefined' && window.location.protocol === 'https:' ? '✅ Secure' : '⚠️ HTTP (OK for localhost)'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="text-center mt-8">
          <a
            href="/"
            className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors"
          >
            ← Back to Main Page
          </a>
        </div>
      </div>
    </div>
  );
}
