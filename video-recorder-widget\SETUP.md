# GoHighLevel Video Recorder Setup Guide

This guide will walk you through setting up the complete video recording system for GoHighLevel funnels.

## Overview

The system consists of:
- **Frontend**: Next.js app with video recording component
- **Storage**: Cloudflare R2 for video files
- **Backend**: Supabase Edge Functions for GHL integration
- **Database**: Supabase PostgreSQL for tracking recordings

## Prerequisites

- Node.js 18+ installed
- Cloudflare account with R2 access
- Supabase account
- GoHighLevel account with API access

## 1. Cloudflare R2 Setup

### Create R2 Bucket
1. Log into Cloudflare Dashboard
2. Go to R2 Object Storage
3. Create a new bucket (e.g., `ghl-video-recordings`)
4. Note your Account ID from the sidebar

### Generate API Tokens
1. Go to "Manage R2 API tokens"
2. Create a new token with:
   - **Permissions**: Object Read & Write
   - **Bucket**: Your created bucket
3. Save the Access Key ID and Secret Access Key

### Configure CORS (Optional)
If you plan to upload directly from the browser:
```json
[
  {
    "AllowedOrigins": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST"],
    "AllowedHeaders": ["*"],
    "MaxAgeSeconds": 3000
  }
]
```

## 2. Supabase Setup

### Create Project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Wait for the database to be ready
4. Note your Project URL and API keys

### Run Migrations
```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link your project
supabase link --project-ref YOUR_PROJECT_ID

# Run migrations
supabase db push
```

### Deploy Edge Function
```bash
# Deploy the GHL update function
supabase functions deploy update-ghl-contact

# Set environment variables
supabase secrets set GHL_API_KEY=your_ghl_api_key
supabase secrets set GHL_LOCATION_ID=your_ghl_location_id
```

## 3. GoHighLevel Setup

### Get API Access
1. Go to Settings > Integrations > API
2. Create a new API key
3. Note your Location ID from the URL or API

### Create Custom Field
1. Go to Settings > Custom Fields
2. Create a new field:
   - **Name**: Video Recording URL
   - **Type**: Text/URL
   - **Field Key**: `video_recording_url`

## 4. Environment Configuration

Create `.env.local` file:
```env
# Cloudflare R2
NEXT_PUBLIC_R2_BUCKET_NAME=your-bucket-name
NEXT_PUBLIC_R2_REGION=auto
NEXT_PUBLIC_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY_ID=your-access-key
R2_SECRET_ACCESS_KEY=your-secret-key

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# GoHighLevel
GHL_API_KEY=your-ghl-api-key
GHL_LOCATION_ID=your-location-id

# App
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 5. Installation & Development

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build
npm start
```

## 6. Embedding in GoHighLevel

### Basic Embed Code
```html
<iframe 
  src="https://your-domain.com/embed?contactId={{contact.id}}"
  width="100%" 
  height="600"
  frameborder="0">
</iframe>
```

### Advanced Integration
For better integration, you can pass contact information via URL parameters:
```html
<script>
  const contactId = '{{contact.id}}';
  const embedUrl = `https://your-domain.com/embed?contactId=${contactId}&firstName={{contact.first_name}}&lastName={{contact.last_name}}`;
  document.write(`<iframe src="${embedUrl}" width="100%" height="600" frameborder="0"></iframe>`);
</script>
```

## 7. Deployment Options

### Vercel (Recommended)
1. Connect your GitHub repository
2. Add environment variables in Vercel dashboard
3. Deploy automatically

### Netlify
1. Connect repository
2. Build command: `npm run build`
3. Publish directory: `.next`
4. Add environment variables

### Self-hosted
1. Build the application: `npm run build`
2. Start with: `npm start`
3. Use PM2 or similar for process management

## 8. Testing

### Test Video Recording
1. Open the app in browser
2. Allow camera/microphone permissions
3. Record a short video
4. Verify upload to R2
5. Check GoHighLevel contact for video URL

### Test API Endpoints
```bash
# Test presigned URL generation
curl -X POST http://localhost:3000/api/presigned-url \
  -H "Content-Type: application/json" \
  -d '{"contactId": "test-contact"}'

# Test video upload
curl -X POST http://localhost:3000/api/upload-video \
  -F "video=@test-video.webm" \
  -F "contactId=test-contact"
```

## 9. Troubleshooting

### Common Issues

**Camera/Microphone not working**
- Check browser permissions
- Ensure HTTPS in production
- Test in different browsers

**Upload failures**
- Verify R2 credentials
- Check CORS configuration
- Monitor browser console for errors

**GHL integration not working**
- Verify API key permissions
- Check custom field configuration
- Review Supabase function logs

### Monitoring
- Check Supabase logs for edge function errors
- Monitor R2 usage and costs
- Set up alerts for failed uploads

## 10. Security Considerations

- Use environment variables for all secrets
- Implement rate limiting for uploads
- Validate file types and sizes
- Consider implementing user authentication
- Regular security audits of dependencies

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review browser console errors
3. Check Supabase function logs
4. Verify all environment variables are set correctly
