import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { createClient } from '@supabase/supabase-js';

// Initialize Cloudflare R2 client
const r2Client = new S3Client({
  region: process.env.NEXT_PUBLIC_R2_REGION || 'auto',
  endpoint: process.env.NEXT_PUBLIC_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  },
});

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const videoFile = formData.get('video') as File;
    const contactId = formData.get('contactId') as string;

    if (!videoFile) {
      return NextResponse.json(
        { error: 'No video file provided' },
        { status: 400 }
      );
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2);
    const fileName = `videos/${timestamp}-${randomId}.webm`;

    // Convert file to buffer
    const arrayBuffer = await videoFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload to Cloudflare R2
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.NEXT_PUBLIC_R2_BUCKET_NAME!,
      Key: fileName,
      Body: buffer,
      ContentType: 'video/webm',
      Metadata: {
        'uploaded-at': new Date().toISOString(),
        'contact-id': contactId || 'unknown',
      },
    });

    await r2Client.send(uploadCommand);

    // Construct the public URL
    const videoUrl = `${process.env.NEXT_PUBLIC_R2_ENDPOINT}/${process.env.NEXT_PUBLIC_R2_BUCKET_NAME}/${fileName}`;

    // If contactId is provided, update GoHighLevel contact via Supabase Edge Function
    if (contactId) {
      try {
        const { data, error } = await supabase.functions.invoke('update-ghl-contact', {
          body: {
            contactId,
            videoUrl,
            customFieldName: 'video_recording_url', // This should match your GHL custom field
          },
        });

        if (error) {
          console.error('Error updating GHL contact:', error);
          // Don't fail the upload if GHL update fails
        }
      } catch (ghlError) {
        console.error('Error calling Supabase function:', ghlError);
        // Don't fail the upload if GHL update fails
      }
    }

    return NextResponse.json({
      success: true,
      videoUrl,
      fileName,
      message: 'Video uploaded successfully',
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload video' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Video upload endpoint. Use POST to upload videos.' },
    { status: 200 }
  );
}
