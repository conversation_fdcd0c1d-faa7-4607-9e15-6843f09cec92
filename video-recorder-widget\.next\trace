[{"name": "hot-reloader", "duration": 104, "timestamp": 12050336718, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1750614413433, "traceId": "2c31b901048e6718"}, {"name": "setup-dev-bundler", "duration": 1007308, "timestamp": 12049941482, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750614413037, "traceId": "2c31b901048e6718"}, {"name": "run-instrumentation-hook", "duration": 51, "timestamp": 12051096870, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750614414193, "traceId": "2c31b901048e6718"}, {"name": "start-dev-server", "duration": 2306373, "timestamp": 12048863163, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "12270243840", "memory.totalMem": "34241683456", "memory.heapSizeLimit": "17170432000", "memory.rss": "175198208", "memory.heapTotal": "103727104", "memory.heapUsed": "64611120"}, "startTime": 1750614411959, "traceId": "2c31b901048e6718"}, {"name": "compile-path", "duration": 3980753, "timestamp": 12075283594, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750614438379, "traceId": "2c31b901048e6718"}, {"name": "ensure-page", "duration": 3982592, "timestamp": 12075282358, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750614438378, "traceId": "2c31b901048e6718"}]