'use client';

import React, { useState } from 'react';
import { Copy, Check, ExternalLink } from 'lucide-react';

interface EmbedCodes {
  iframe: string;
  javascript: string;
  gohighlevel: string;
  responsive: string;
}

export default function EmbedCodeGenerator() {
  const [embedCodes, setEmbedCodes] = useState<EmbedCodes | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  
  // Form state
  const [contactId, setContactId] = useState('{{contact.id}}');
  const [firstName, setFirstName] = useState('{{contact.first_name}}');
  const [lastName, setLastName] = useState('{{contact.last_name}}');
  const [width, setWidth] = useState('100%');
  const [height, setHeight] = useState('600');
  const [customDomain, setCustomDomain] = useState('');

  const generateEmbedCode = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/embed-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactId,
          firstName,
          lastName,
          width,
          height,
          customDomain,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate embed code');
      }

      const data = await response.json();
      setEmbedCodes(data.embedCodes);
      setPreviewUrl(data.previewUrl);
    } catch (error) {
      console.error('Error generating embed code:', error);
      alert('Failed to generate embed code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (code: string, type: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(type);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      alert('Failed to copy to clipboard');
    }
  };

  return (
    <div className="space-y-6">
      {/* Configuration Form */}
      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Contact ID
          </label>
          <input
            type="text"
            value={contactId}
            onChange={(e) => setContactId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            placeholder="{{contact.id}}"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            First Name
          </label>
          <input
            type="text"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            placeholder="{{contact.first_name}}"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Last Name
          </label>
          <input
            type="text"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            placeholder="{{contact.last_name}}"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Custom Domain (Optional)
          </label>
          <input
            type="text"
            value={customDomain}
            onChange={(e) => setCustomDomain(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            placeholder="https://your-domain.com"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Width
          </label>
          <input
            type="text"
            value={width}
            onChange={(e) => setWidth(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            placeholder="100%"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Height
          </label>
          <input
            type="text"
            value={height}
            onChange={(e) => setHeight(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            placeholder="600"
          />
        </div>
      </div>

      {/* Generate Button */}
      <div className="flex gap-3">
        <button
          onClick={generateEmbedCode}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-md transition-colors"
        >
          {loading ? 'Generating...' : 'Generate Embed Code'}
        </button>
        
        {previewUrl && (
          <a
            href={previewUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors flex items-center gap-2"
          >
            <ExternalLink className="w-4 h-4" />
            Preview
          </a>
        )}
      </div>

      {/* Generated Embed Codes */}
      {embedCodes && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-800">Generated Embed Codes</h3>
          
          {/* GoHighLevel Embed */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-gray-700">GoHighLevel Funnel (Recommended)</h4>
              <button
                onClick={() => copyToClipboard(embedCodes.gohighlevel, 'gohighlevel')}
                className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
              >
                {copiedCode === 'gohighlevel' ? (
                  <>
                    <Check className="w-4 h-4" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    Copy
                  </>
                )}
              </button>
            </div>
            <pre className="bg-gray-50 p-3 rounded text-xs overflow-x-auto">
              <code>{embedCodes.gohighlevel}</code>
            </pre>
          </div>

          {/* Basic iframe */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-gray-700">Basic iframe</h4>
              <button
                onClick={() => copyToClipboard(embedCodes.iframe, 'iframe')}
                className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
              >
                {copiedCode === 'iframe' ? (
                  <>
                    <Check className="w-4 h-4" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    Copy
                  </>
                )}
              </button>
            </div>
            <pre className="bg-gray-50 p-3 rounded text-xs overflow-x-auto">
              <code>{embedCodes.iframe}</code>
            </pre>
          </div>

          {/* Responsive Embed */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-gray-700">Responsive Embed</h4>
              <button
                onClick={() => copyToClipboard(embedCodes.responsive, 'responsive')}
                className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
              >
                {copiedCode === 'responsive' ? (
                  <>
                    <Check className="w-4 h-4" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    Copy
                  </>
                )}
              </button>
            </div>
            <pre className="bg-gray-50 p-3 rounded text-xs overflow-x-auto">
              <code>{embedCodes.responsive}</code>
            </pre>
          </div>

          {/* JavaScript Embed */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-gray-700">JavaScript Embed</h4>
              <button
                onClick={() => copyToClipboard(embedCodes.javascript, 'javascript')}
                className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
              >
                {copiedCode === 'javascript' ? (
                  <>
                    <Check className="w-4 h-4" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    Copy
                  </>
                )}
              </button>
            </div>
            <pre className="bg-gray-50 p-3 rounded text-xs overflow-x-auto">
              <code>{embedCodes.javascript}</code>
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
