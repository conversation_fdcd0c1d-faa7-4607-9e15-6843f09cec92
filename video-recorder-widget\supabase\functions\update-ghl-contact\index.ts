import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RequestBody {
  contactId: string;
  videoUrl: string;
  customFieldName?: string;
  fileName?: string;
  fileSize?: number;
  duration?: number;
}

interface GHLContact {
  id: string;
  customFields?: Record<string, any>;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    const { contactId, videoUrl, customFieldName = 'video_recording_url', fileName, fileSize, duration }: RequestBody = await req.json()

    if (!contactId || !videoUrl) {
      return new Response(
        JSON.stringify({ error: 'contactId and videoUrl are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get GHL API credentials from environment
    const ghlApiKey = Deno.env.get('GHL_API_KEY')
    const ghlLocationId = Deno.env.get('GHL_LOCATION_ID')

    if (!ghlApiKey) {
      throw new Error('GHL_API_KEY not configured')
    }

    // First, save the recording to our database
    const { data: recording, error: dbError } = await supabaseClient
      .from('video_recordings')
      .insert({
        contact_id: contactId,
        video_url: videoUrl,
        file_name: fileName || 'unknown',
        file_size: fileSize,
        duration: duration,
        ghl_updated: false
      })
      .select()
      .single()

    if (dbError) {
      console.error('Database error:', dbError)
      // Continue with GHL update even if DB save fails
    }

    // Update GoHighLevel contact
    const ghlResponse = await fetch(`https://services.leadconnectorhq.com/contacts/${contactId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${ghlApiKey}`,
        'Content-Type': 'application/json',
        'Version': '2021-07-28'
      },
      body: JSON.stringify({
        customFields: {
          [customFieldName]: videoUrl
        }
      })
    })

    if (!ghlResponse.ok) {
      const errorText = await ghlResponse.text()
      console.error('GHL API Error:', errorText)
      
      // Update our database record with the error
      if (recording) {
        await supabaseClient
          .from('video_recordings')
          .update({ 
            ghl_error: `HTTP ${ghlResponse.status}: ${errorText}`,
            ghl_updated: false 
          })
          .eq('id', recording.id)
      }

      return new Response(
        JSON.stringify({ 
          error: 'Failed to update GoHighLevel contact',
          details: errorText,
          status: ghlResponse.status
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const ghlData = await ghlResponse.json()

    // Update our database record as successful
    if (recording) {
      await supabaseClient
        .from('video_recordings')
        .update({ ghl_updated: true, ghl_error: null })
        .eq('id', recording.id)
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'Contact updated successfully',
        contactId,
        videoUrl,
        customFieldName,
        ghlResponse: ghlData
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Function error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
