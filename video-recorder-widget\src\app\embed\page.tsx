'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import VideoRecorder from '@/components/VideoRecorder';

export default function EmbedPage() {
  const searchParams = useSearchParams();
  const [contactId, setContactId] = useState<string>('');
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [uploadedVideoUrl, setUploadedVideoUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get parameters from URL
    const contactIdParam = searchParams.get('contactId') || '';
    const firstNameParam = searchParams.get('firstName') || '';
    const lastNameParam = searchParams.get('lastName') || '';

    setContactId(contactIdParam);
    setFirstName(firstNameParam);
    setLastName(lastNameParam);
    setIsLoading(false);
  }, [searchParams]);

  const handleVideoUploaded = (videoUrl: string) => {
    setUploadedVideoUrl(videoUrl);
    
    // Notify parent window if embedded in iframe
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'VIDEO_UPLOADED',
        data: {
          videoUrl,
          contactId,
          firstName,
          lastName
        }
      }, '*');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading video recorder...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Record Your Video Message
          </h1>
          {firstName && (
            <p className="text-lg text-gray-600">
              Hi {firstName}! Please record your video below.
            </p>
          )}
        </div>

        {/* Contact Info Display (for debugging) */}
        {contactId && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-sm">
            <p className="text-blue-800">
              <strong>Contact:</strong> {firstName} {lastName} (ID: {contactId})
            </p>
          </div>
        )}

        {/* Video Recorder */}
        <div className="mb-8">
          <VideoRecorder
            onVideoUploaded={handleVideoUploaded}
            contactId={contactId || undefined}
            maxDuration={300}
            className="mx-auto"
          />
        </div>

        {/* Success Message */}
        {uploadedVideoUrl && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
            <div className="text-green-600 text-6xl mb-4">✅</div>
            <h3 className="text-xl font-semibold text-green-800 mb-2">
              Video Recorded Successfully!
            </h3>
            <p className="text-green-700 mb-4">
              Your video has been saved and will be reviewed shortly.
            </p>
            {firstName && (
              <p className="text-green-600">
                Thank you, {firstName}!
              </p>
            )}
          </div>
        )}

        {/* Instructions */}
        {!uploadedVideoUrl && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Recording Instructions
            </h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Click the red record button to start recording</li>
              <li>• Speak clearly and look at the camera</li>
              <li>• Keep your message under 5 minutes</li>
              <li>• Click stop when finished, then review your video</li>
              <li>• Click upload to save your recording</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
