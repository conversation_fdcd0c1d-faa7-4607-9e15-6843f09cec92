# GoHighLevel Video Recorder Widget

A powerful video recording widget that integrates seamlessly with GoHighLevel funnels. Users can record videos directly in your funnels, which are automatically uploaded to Cloudflare R2 and saved to their GoHighLevel contact records.

## 🚀 Features

- **📹 Browser-based Video Recording**: Record videos directly in the browser using MediaRecorder API
- **☁️ Cloudflare R2 Storage**: Secure, fast, and cost-effective video storage
- **🔗 GoHighLevel Integration**: Automatically update contact records with video URLs
- **📱 Responsive Design**: Works on desktop and mobile devices
- **🎨 Embeddable Widget**: Easy to embed in any GoHighLevel funnel
- **⚡ Real-time Upload**: Videos are uploaded immediately after recording
- **🔒 Secure**: Environment-based configuration for all API keys

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Storage**: Cloudflare R2 (S3-compatible)
- **Backend**: Supabase Edge Functions
- **Database**: Supabase PostgreSQL
- **Icons**: Lucide React
- **Deployment**: Vercel (recommended)

## 📋 Prerequisites

Before setting up the project, ensure you have:

- Node.js 18+ installed
- A Cloudflare account with R2 access
- A Supabase account
- A GoHighLevel account with API access
- Basic knowledge of environment variables

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <your-repo-url>
cd video-recorder-widget
npm install
```

### 2. Environment Setup

Create a `.env.local` file:

```env
# Cloudflare R2 Configuration
NEXT_PUBLIC_R2_BUCKET_NAME=your-video-bucket
NEXT_PUBLIC_R2_REGION=auto
NEXT_PUBLIC_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY_ID=your-r2-access-key
R2_SECRET_ACCESS_KEY=your-r2-secret-key

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# GoHighLevel Configuration
GHL_API_KEY=your-ghl-api-key
GHL_LOCATION_ID=your-ghl-location-id

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 3. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📖 Detailed Setup Guide

For complete setup instructions including Cloudflare R2, Supabase, and GoHighLevel configuration, see [SETUP.md](./SETUP.md).

## 🎯 Usage

### Basic Integration

1. **Generate Embed Code**: Use the built-in embed code generator on the main page
2. **Copy Code**: Choose the appropriate embed type (GoHighLevel recommended)
3. **Add to Funnel**: Paste the code into a Custom HTML element in your GoHighLevel funnel
4. **Test**: Verify the integration with a test contact

### Embed Options

- **GoHighLevel Funnel**: Optimized for GHL with merge fields
- **Basic iframe**: Simple iframe embed
- **Responsive**: Adapts to container width
- **JavaScript**: Dynamic embed with more flexibility

### Example GoHighLevel Embed

```html
<div class="video-recorder-container">
  <iframe
    src="https://your-domain.com/embed?contactId={{contact.id}}&firstName={{contact.first_name}}&lastName={{contact.last_name}}"
    width="100%"
    height="600"
    frameborder="0"
    allow="camera; microphone"
    style="border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); width: 100%; max-width: 600px;">
  </iframe>
</div>
```

## 🏗️ Project Structure

```
video-recorder-widget/
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── upload-video/route.ts      # Video upload endpoint
│   │   │   ├── presigned-url/route.ts     # Presigned URL generation
│   │   │   └── embed-code/route.ts        # Embed code generator
│   │   ├── embed/page.tsx                 # Embeddable page
│   │   └── page.tsx                       # Main demo page
│   ├── components/
│   │   ├── VideoRecorder.tsx              # Main video recording component
│   │   └── EmbedCodeGenerator.tsx         # Embed code generator UI
│   └── lib/
│       └── r2-client.ts                   # Cloudflare R2 utilities
├── supabase/
│   ├── functions/
│   │   └── update-ghl-contact/index.ts    # Edge function for GHL integration
│   └── migrations/
│       └── 001_create_video_recordings.sql
├── SETUP.md                               # Detailed setup guide
└── README.md                              # This file
```

## 🔧 API Endpoints

### POST `/api/upload-video`
Upload video files to Cloudflare R2 and update GoHighLevel contacts.

**Body**: FormData with `video` file and optional `contactId`

### POST `/api/presigned-url`
Generate presigned URLs for direct browser uploads.

**Body**: `{ contactId?: string, contentType?: string }`

### POST `/api/embed-code`
Generate embed codes for different platforms.

**Body**: `{ contactId?, firstName?, lastName?, width?, height?, customDomain? }`

## 🎨 Customization

### Styling
The widget uses Tailwind CSS for styling. You can customize the appearance by:

1. Modifying the Tailwind classes in components
2. Adding custom CSS for specific branding
3. Using CSS variables for theme colors

### Recording Settings
Adjust recording parameters in `VideoRecorder.tsx`:

```typescript
const stream = await navigator.mediaDevices.getUserMedia({
  video: {
    width: { ideal: 1280 },
    height: { ideal: 720 },
    facingMode: 'user'
  },
  audio: true
});
```

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Add environment variables in the Vercel dashboard
3. Deploy automatically on push

### Other Platforms

- **Netlify**: Set build command to `npm run build`
- **Railway**: Configure environment variables and deploy
- **Self-hosted**: Use `npm run build && npm start`

## 📊 Monitoring & Analytics

### Supabase Dashboard
- Monitor edge function logs
- Track video recording statistics
- View database records

### Cloudflare Analytics
- Monitor R2 storage usage
- Track bandwidth costs
- Set up usage alerts

## 🔒 Security Considerations

- All API keys are stored as environment variables
- Videos are stored in private R2 buckets
- CORS is configured for specific domains
- Rate limiting should be implemented for production
- Consider implementing user authentication for sensitive use cases

## 🐛 Troubleshooting

### Common Issues

**Camera/Microphone Access Denied**
- Ensure HTTPS in production
- Check browser permissions
- Test in different browsers

**Upload Failures**
- Verify R2 credentials and bucket configuration
- Check CORS settings
- Monitor browser console for errors

**GoHighLevel Integration Issues**
- Verify API key permissions
- Check custom field configuration
- Review Supabase function logs

### Debug Mode

Enable debug logging by adding to your environment:
```env
NODE_ENV=development
```

## 📈 Performance Optimization

- Videos are compressed using WebM format
- Chunked uploads for large files
- Lazy loading of components
- Optimized bundle size with Next.js

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

1. Check the [SETUP.md](./SETUP.md) guide
2. Review the troubleshooting section
3. Check browser console for errors
4. Verify all environment variables are configured

## 🎯 Roadmap

- [ ] Video compression options
- [ ] Multiple video format support
- [ ] Batch upload capabilities
- [ ] Advanced analytics dashboard
- [ ] White-label customization options
- [ ] Mobile app integration
