'use client';

import { useState } from 'react';
import VideoRecorder from '@/components/VideoRecorder';
import EmbedCodeGenerator from '@/components/EmbedCodeGenerator';

export default function Home() {
  const [uploadedVideoUrl, setUploadedVideoUrl] = useState<string | null>(null);
  const [contactId, setContactId] = useState('');

  const handleVideoUploaded = (videoUrl: string) => {
    setUploadedVideoUrl(videoUrl);
    console.log('Video uploaded:', videoUrl);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            GoHighLevel Video Recorder
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-6">
            Record videos directly in your funnels and automatically save them to your GoHighLevel contacts
          </p>
          <div className="flex justify-center gap-4">
            <a
              href="/test"
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md transition-colors"
            >
              🧪 Test Page
            </a>
            <a
              href="/embed"
              className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-md transition-colors"
            >
              📱 Embed Preview
            </a>
          </div>
        </div>

        {/* Configuration Panel */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Configuration</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="contactId" className="block text-sm font-medium text-gray-700 mb-2">
                Contact ID (Optional)
              </label>
              <input
                type="text"
                id="contactId"
                value={contactId}
                onChange={(e) => setContactId(e.target.value)}
                placeholder="Enter GoHighLevel Contact ID"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-sm text-gray-500 mt-1">
                If provided, the video URL will be saved to this contact's custom field
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Features</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Record up to 5 minutes of video</li>
                <li>• Automatic upload to Cloudflare R2</li>
                <li>• GoHighLevel contact integration</li>
                <li>• Embeddable in any funnel</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Video Recorder */}
        <div className="mb-8">
          <VideoRecorder
            onVideoUploaded={handleVideoUploaded}
            contactId={contactId || undefined}
            maxDuration={300}
            className="mx-auto"
          />
        </div>

        {/* Upload Result */}
        {uploadedVideoUrl && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              ✅ Video Uploaded Successfully!
            </h3>
            <p className="text-green-700 mb-3">
              Your video has been uploaded and is ready to use.
            </p>
            <div className="bg-white rounded border p-3">
              <p className="text-sm text-gray-600 mb-1">Video URL:</p>
              <code className="text-sm bg-gray-100 px-2 py-1 rounded break-all">
                {uploadedVideoUrl}
              </code>
            </div>
            {contactId && (
              <p className="text-sm text-green-600 mt-2">
                📝 Contact {contactId} has been updated with the video URL
              </p>
            )}
          </div>
        )}

        {/* Embed Code Generator */}
        <div className="bg-white rounded-lg shadow-lg p-6 mt-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Embed Code Generator</h2>
          <EmbedCodeGenerator />
        </div>

        {/* Integration Instructions */}
        <div className="bg-white rounded-lg shadow-lg p-6 mt-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Integration Guide</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-3">Embed in GoHighLevel</h3>
              <ol className="text-sm text-gray-600 space-y-2">
                <li>1. Use the embed code generator above</li>
                <li>2. Add a Custom HTML element to your funnel</li>
                <li>3. Paste the generated embed code</li>
                <li>4. Test with a sample contact</li>
              </ol>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-3">Setup Requirements</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Cloudflare R2 bucket configured</li>
                <li>• Supabase project with edge function</li>
                <li>• GoHighLevel API access</li>
                <li>• Custom field for video URLs</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
