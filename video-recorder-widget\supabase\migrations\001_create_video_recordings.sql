-- Create table for tracking video recordings
CREATE TABLE IF NOT EXISTS video_recordings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    contact_id TEXT NOT NULL,
    video_url TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_size BIGINT,
    duration INTEGER, -- in seconds
    ghl_updated BOOLEAN DEFAULT FALSE,
    ghl_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_video_recordings_contact_id ON video_recordings(contact_id);
CREATE INDEX IF NOT EXISTS idx_video_recordings_created_at ON video_recordings(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- <PERSON>reate trigger to automatically update updated_at
CREATE TRIGGER update_video_recordings_updated_at 
    BEFORE UPDATE ON video_recordings 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE video_recordings ENABLE ROW LEVEL SECURITY;

-- Create policy for service role (full access)
CREATE POLICY "Service role can manage all video recordings" ON video_recordings
    FOR ALL USING (auth.role() = 'service_role');

-- Create policy for authenticated users (read their own recordings)
CREATE POLICY "Users can view their own video recordings" ON video_recordings
    FOR SELECT USING (auth.uid()::text = contact_id OR auth.role() = 'authenticated');
