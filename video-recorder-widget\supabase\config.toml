project_id = "your-project-id"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
port = 54322
external_url = "http://localhost:54321"
jwt_expiry = 3600
refresh_token_rotation_enabled = true
refresh_token_reuse_interval = 10
enable_signup = true

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[db]
port = 54322
shadow_port = 54320
major_version = 15

[functions]
verify_jwt = false

[edge_functions]
verify_jwt = false

[storage]
enabled = true
port = 54323
file_size_limit = "50MiB"
s3_enabled = false

[inbucket]
enabled = true
port = 54324
smtp_port = 54325
pop3_port = 54326

[realtime]
enabled = true
port = 54327

[studio]
enabled = true
port = 54323
api_url = "http://localhost:54321"
