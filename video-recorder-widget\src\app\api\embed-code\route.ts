import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      contactId = '{{contact.id}}',
      firstName = '{{contact.first_name}}',
      lastName = '{{contact.last_name}}',
      width = '100%',
      height = '600',
      customDomain
    } = body;

    const baseUrl = customDomain || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    
    // Generate different embed code options
    const embedCodes = {
      // Basic iframe embed
      iframe: `<iframe 
  src="${baseUrl}/embed?contactId=${contactId}&firstName=${firstName}&lastName=${lastName}"
  width="${width}" 
  height="${height}"
  frameborder="0"
  allow="camera; microphone"
  style="border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
</iframe>`,

      // JavaScript embed with dynamic contact ID
      javascript: `<div id="video-recorder-widget"></div>
<script>
(function() {
  const contactId = '${contactId}';
  const firstName = '${firstName}';
  const lastName = '${lastName}';
  
  const iframe = document.createElement('iframe');
  iframe.src = \`${baseUrl}/embed?contactId=\${contactId}&firstName=\${firstName}&lastName=\${lastName}\`;
  iframe.width = '${width}';
  iframe.height = '${height}';
  iframe.frameBorder = '0';
  iframe.allow = 'camera; microphone';
  iframe.style.cssText = 'border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);';
  
  document.getElementById('video-recorder-widget').appendChild(iframe);
})();
</script>`,

      // GoHighLevel specific embed
      gohighlevel: `<div class="video-recorder-container">
  <iframe 
    src="${baseUrl}/embed?contactId={{contact.id}}&firstName={{contact.first_name}}&lastName={{contact.last_name}}"
    width="${width}" 
    height="${height}"
    frameborder="0"
    allow="camera; microphone"
    style="border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); width: 100%; max-width: 600px;">
  </iframe>
</div>

<style>
.video-recorder-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}
</style>`,

      // Responsive embed
      responsive: `<div style="position: relative; width: 100%; max-width: 600px; margin: 0 auto;">
  <div style="position: relative; padding-bottom: 75%; height: 0; overflow: hidden;">
    <iframe 
      src="${baseUrl}/embed?contactId=${contactId}&firstName=${firstName}&lastName=${lastName}"
      style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);"
      allow="camera; microphone">
    </iframe>
  </div>
</div>`
    };

    return NextResponse.json({
      success: true,
      embedCodes,
      previewUrl: `${baseUrl}/embed?contactId=${contactId}&firstName=${firstName}&lastName=${lastName}`,
      instructions: {
        iframe: "Basic iframe embed - paste directly into HTML",
        javascript: "JavaScript embed - more flexible, good for dynamic content",
        gohighlevel: "Optimized for GoHighLevel funnels with merge fields",
        responsive: "Responsive embed that adapts to container width"
      }
    });

  } catch (error) {
    console.error('Embed code generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate embed code' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Embed code generation endpoint. Use POST to generate embed codes.',
    example: {
      contactId: '{{contact.id}}',
      firstName: '{{contact.first_name}}',
      lastName: '{{contact.last_name}}',
      width: '100%',
      height: '600',
      customDomain: 'https://your-domain.com'
    }
  });
}
