import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Initialize Cloudflare R2 client
export const r2Client = new S3Client({
  region: process.env.NEXT_PUBLIC_R2_REGION || 'auto',
  endpoint: process.env.NEXT_PUBLIC_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  },
});

export interface PresignedUrlOptions {
  fileName: string;
  contentType?: string;
  expiresIn?: number; // seconds
  metadata?: Record<string, string>;
}

export async function generatePresignedUrl({
  fileName,
  contentType = 'video/webm',
  expiresIn = 3600, // 1 hour
  metadata = {},
}: PresignedUrlOptions) {
  const command = new PutObjectCommand({
    Bucket: process.env.NEXT_PUBLIC_R2_BUCKET_NAME!,
    Key: fileName,
    ContentType: contentType,
    Metadata: metadata,
  });

  const presignedUrl = await getSignedUrl(r2Client, command, {
    expiresIn,
  });

  return {
    presignedUrl,
    publicUrl: `${process.env.NEXT_PUBLIC_R2_ENDPOINT}/${process.env.NEXT_PUBLIC_R2_BUCKET_NAME}/${fileName}`,
    fileName,
  };
}

export function generateVideoFileName(contactId?: string): string {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2);
  const prefix = contactId ? `contact-${contactId}` : 'anonymous';
  return `videos/${prefix}/${timestamp}-${randomId}.webm`;
}
