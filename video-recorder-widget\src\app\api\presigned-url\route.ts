import { NextRequest, NextResponse } from 'next/server';
import { generatePresignedUrl, generateVideoFileName } from '@/lib/r2-client';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { contactId, contentType = 'video/webm' } = body;

    // Generate unique filename
    const fileName = generateVideoFileName(contactId);

    // Generate presigned URL
    const result = await generatePresignedUrl({
      fileName,
      contentType,
      metadata: {
        'uploaded-at': new Date().toISOString(),
        'contact-id': contactId || 'unknown',
      },
    });

    return NextResponse.json({
      success: true,
      ...result,
    });

  } catch (error) {
    console.error('Presigned URL generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate presigned URL' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Presigned URL generation endpoint. Use POST to get presigned URLs.' },
    { status: 200 }
  );
}
