'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Play, Square, RotateCcw, Upload, Camera, Loader2 } from 'lucide-react';

interface VideoRecorderProps {
  onVideoUploaded?: (videoUrl: string) => void;
  contactId?: string;
  maxDuration?: number; // in seconds
  className?: string;
}

type RecordingState = 'idle' | 'recording' | 'stopped' | 'uploading';

export default function VideoRecorder({ 
  onVideoUploaded, 
  contactId, 
  maxDuration = 300, // 5 minutes default
  className = '' 
}: VideoRecorderProps) {
  const [recordingState, setRecordingState] = useState<RecordingState>('idle');
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const videoRef = useRef<HTMLVideoElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const startRecording = useCallback(async () => {
    try {
      setError(null);
      
      // Request camera and microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        },
        audio: true
      });

      streamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      // Create MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'video/webm;codecs=vp9,opus'
      });

      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'video/webm' });
        setRecordedBlob(blob);
        setRecordingState('stopped');
        
        // Create preview URL
        if (videoRef.current) {
          videoRef.current.srcObject = null;
          videoRef.current.src = URL.createObjectURL(blob);
        }
      };

      // Start recording
      mediaRecorder.start(1000); // Collect data every second
      setRecordingState('recording');
      setRecordingTime(0);

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1;
          if (newTime >= maxDuration) {
            stopRecording();
          }
          return newTime;
        });
      }, 1000);

    } catch (err) {
      console.error('Error starting recording:', err);
      setError('Failed to access camera/microphone. Please check permissions.');
    }
  }, [maxDuration]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && recordingState === 'recording') {
      mediaRecorderRef.current.stop();
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, [recordingState]);

  const resetRecording = useCallback(() => {
    setRecordedBlob(null);
    setRecordingState('idle');
    setRecordingTime(0);
    setError(null);
    setUploadProgress(0);

    if (videoRef.current) {
      videoRef.current.src = '';
      videoRef.current.srcObject = null;
    }
  }, []);

  const uploadVideo = useCallback(async () => {
    if (!recordedBlob) return;

    try {
      setRecordingState('uploading');
      setUploadProgress(0);

      // Create FormData for upload
      const formData = new FormData();
      formData.append('video', recordedBlob, `recording-${Date.now()}.webm`);
      if (contactId) {
        formData.append('contactId', contactId);
      }

      // Upload to our API endpoint
      const response = await fetch('/api/upload-video', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const result = await response.json();
      
      if (result.videoUrl) {
        onVideoUploaded?.(result.videoUrl);
        setUploadProgress(100);
        // Reset after successful upload
        setTimeout(resetRecording, 2000);
      }

    } catch (err) {
      console.error('Upload error:', err);
      setError('Failed to upload video. Please try again.');
      setRecordingState('stopped');
    }
  }, [recordedBlob, contactId, onVideoUploaded, resetRecording]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      <div className="space-y-4">
        {/* Video Preview */}
        <div className="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
          <video
            ref={videoRef}
            autoPlay
            muted={recordingState === 'recording'}
            controls={recordingState === 'stopped'}
            className="w-full h-full object-cover"
          />
          
          {recordingState === 'idle' && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Camera className="w-16 h-16 text-gray-400" />
            </div>
          )}

          {recordingState === 'recording' && (
            <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
              REC {formatTime(recordingTime)}
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="flex justify-center gap-3">
          {recordingState === 'idle' && (
            <button
              onClick={startRecording}
              className="bg-red-500 hover:bg-red-600 text-white p-3 rounded-full transition-colors"
              title="Start Recording"
            >
              <Play className="w-6 h-6" />
            </button>
          )}

          {recordingState === 'recording' && (
            <button
              onClick={stopRecording}
              className="bg-gray-800 hover:bg-gray-900 text-white p-3 rounded-full transition-colors"
              title="Stop Recording"
            >
              <Square className="w-6 h-6" />
            </button>
          )}

          {recordingState === 'stopped' && (
            <>
              <button
                onClick={resetRecording}
                className="bg-gray-500 hover:bg-gray-600 text-white p-3 rounded-full transition-colors"
                title="Record Again"
              >
                <RotateCcw className="w-6 h-6" />
              </button>
              <button
                onClick={uploadVideo}
                className="bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full transition-colors"
                title="Upload Video"
              >
                <Upload className="w-6 h-6" />
              </button>
            </>
          )}

          {recordingState === 'uploading' && (
            <div className="bg-blue-500 text-white p-3 rounded-full">
              <Loader2 className="w-6 h-6 animate-spin" />
            </div>
          )}
        </div>

        {/* Upload Progress */}
        {recordingState === 'uploading' && (
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
            {error}
          </div>
        )}

        {/* Recording Info */}
        <div className="text-center text-sm text-gray-500">
          {recordingState === 'idle' && 'Click the record button to start'}
          {recordingState === 'recording' && `Recording... (${formatTime(maxDuration - recordingTime)} remaining)`}
          {recordingState === 'stopped' && 'Review your recording and upload when ready'}
          {recordingState === 'uploading' && 'Uploading your video...'}
        </div>
      </div>
    </div>
  );
}
